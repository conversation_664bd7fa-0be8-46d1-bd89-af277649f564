import { app, <PERSON><PERSON>erWindow, ipcMain } from 'electron'
import { createRequire } from 'node:module'
import { fileURLToPath } from 'node:url'
import path from 'node:path'
import * as https from 'https'
import * as http from 'http'
import * as querystring from 'querystring'

const require = createRequire(import.meta.url)
const __dirname = path.dirname(fileURLToPath(import.meta.url))

// Figma API配置
const FIGMA_TOKEN = "*********************************************"

// HTTP请求工具函数
function makeHttpRequest(url: string, options: any = {}): Promise<any> {
  return new Promise((resolve, reject) => {
    try {
      const parsedUrl = new URL(url)
      const httpModule = parsedUrl.protocol === 'https:' ? https : http

      // 添加查询参数
      if (options.params) {
        const queryString = querystring.stringify(options.params)
        if (queryString) {
          parsedUrl.search = parsedUrl.search
            ? `${parsedUrl.search}&${queryString}`
            : `?${queryString}`
        }
      }

      const requestOptions = {
        hostname: parsedUrl.hostname,
        port: parsedUrl.port || (parsedUrl.protocol === 'https:' ? 443 : 80),
        path: parsedUrl.pathname + parsedUrl.search,
        method: options.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Electron-App/1.0.0',
          ...options.headers
        },
        timeout: options.timeout || 10000
      }

      const req = httpModule.request(requestOptions, (res) => {
        let responseData = ''

        res.on('data', (chunk) => {
          responseData += chunk
        })

        res.on('end', () => {
          try {
            const statusCode = res.statusCode || 0

            if (statusCode >= 400) {
              reject(new Error(`HTTP Error: ${statusCode} ${res.statusMessage}`))
              return
            }

            let parsedData
            const contentType = res.headers['content-type'] || ''

            if (contentType.includes('application/json') && responseData.trim()) {
              try {
                parsedData = JSON.parse(responseData)
              } catch (parseError) {
                parsedData = responseData
              }
            } else {
              parsedData = responseData
            }

            resolve({
              data: parsedData,
              status: statusCode,
              statusText: res.statusMessage || 'OK',
              headers: res.headers
            })
          } catch (error) {
            reject(new Error(`Response parsing error: ${error instanceof Error ? error.message : 'Unknown error'}`))
          }
        })
      })

      req.on('error', (error) => {
        reject(new Error(`Network error: ${error.message}`))
      })

      req.on('timeout', () => {
        req.destroy()
        reject(new Error(`Request timeout after ${requestOptions.timeout}ms`))
      })

      if (options.data && options.method !== 'GET') {
        const postData = typeof options.data === 'object' ? JSON.stringify(options.data) : String(options.data)
        req.write(postData)
      }

      req.end()
    } catch (error) {
      reject(new Error(`Request error: ${error instanceof Error ? error.message : 'Unknown error'}`))
    }
  })
}

// The built directory structure
//
// ├─┬─┬ dist
// │ │ └── index.html
// │ │
// │ ├─┬ dist-electron
// │ │ ├── main.js
// │ │ └── preload.js
// │
process.env.DIST = path.join(__dirname, '../dist')
process.env.VITE_PUBLIC = app.isPackaged ? process.env.DIST : path.join(process.env.DIST, '../public')

let win: BrowserWindow | null

function createWindow() {
  win = new BrowserWindow({
    width: 1200,
    height: 800,
    icon: path.join(process.env.VITE_PUBLIC, 'electron-vite.svg'),
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
    },
  })

  // Test active push message to Renderer-process.
  win.webContents.on('did-finish-load', () => {
    win?.webContents.send('main-process-message', (new Date).toLocaleString())
  })

  if (process.env.VITE_DEV_SERVER_URL) {
    win.loadURL(process.env.VITE_DEV_SERVER_URL)
    win.webContents.openDevTools()
  } else {
    // win.loadFile('dist/index.html')
    win.loadFile(path.join(process.env.DIST, 'index.html'))
  }
}

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
    win = null
  }
})

app.on('activate', () => {
  // On OS X it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow()
  }
})

// IPC处理器
ipcMain.handle('figma-api-request', async (event, { url, params, headers }) => {
  try {
    const response = await makeHttpRequest(url, {
      params,
      headers: {
        'X-FIGMA-TOKEN': FIGMA_TOKEN,
        ...headers
      }
    })
    return { success: true, data: response.data }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
})

// Figma链接解析函数
ipcMain.handle('parse-figma-url', async (event, url: string) => {
  try {
    const parsedUrl = new URL(url)

    if (!parsedUrl.hostname.includes('figma.com')) {
      throw new Error('不是有效的 Figma 链接')
    }

    const pathParts = parsedUrl.pathname.split('/').filter(part => part.length > 0)

    if (pathParts.length < 2 || !['file', 'design'].includes(pathParts[0])) {
      throw new Error('无效的 Figma 链接格式')
    }

    const fileKey = pathParts[1]

    let nodeId: string | undefined
    const nodeIdParam = parsedUrl.searchParams.get('node-id')
    if (nodeIdParam) {
      nodeId = decodeURIComponent(nodeIdParam).replace(':', '-')
    }

    return {
      success: true,
      data: {
        fileKey,
        nodeId,
        originalUrl: url
      }
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
})

app.whenReady().then(createWindow)
